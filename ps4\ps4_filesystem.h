// Copyright 2025 <Copyright Owner>

#pragma once

#include <cstdint>
#include <istream>
#include <mutex>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>
#include <unordered_map>
#include <vector>

// Define ssize_t and mode_t for Windows compatibility
#if defined(_MSC_VER) || defined(_WIN32) || defined(_WIN64)
#include <BaseTsd.h>
typedef SSIZE_T ssize_t;
typedef unsigned int mode_t;
#endif

// Undefine Windows macros that conflict with our method names
#ifdef RemoveDirectoryA
#undef RemoveDirectoryA
#endif
#ifdef RemoveDirectoryW
#undef RemoveDirectoryW
#endif
#ifdef RemoveDirectory
#undef RemoveDirectory
#endif

namespace ps4 {

class PS4Emulator;

/**
 * @brief Exception for filesystem-related errors.
 */
struct FilesystemException : std::runtime_error {
  explicit FilesystemException(const std::string &msg)
      : std::runtime_error(msg) {}
};

/**
 * @brief PS4-specific file types for proper emulation.
 */
enum class PS4FileType {
  Regular,   ///< Regular file
  Directory, ///< Directory
  Device,    ///< Device file (/dev/*)
  Special,   ///< Special file (pipes, sockets)
  PFS,       ///< PlayStation File System
  SaveData,  ///< Save data file
  Trophy,    ///< Trophy data
  System     ///< System file
};

/**
 * @brief Enhanced structure representing a file entry in the virtual
 * filesystem.
 */
struct FileEntry {
  std::string path;              ///< Virtual file path
  std::string hostPath;          ///< Mapped host path
  uint64_t size = 0;             ///< File size in bytes
  int protection = 0;            ///< Memory protection flags (e.g., PROT_READ)
  std::vector<uint8_t> data;     ///< In-memory file data (for caching)
  uint32_t mode = 0;             ///< File permissions
  uint64_t creationTime = 0;     ///< Creation timestamp
  uint64_t modificationTime = 0; ///< Modification timestamp
  uint64_t accessTime = 0;       ///< Last access timestamp
  bool isDir = false;            ///< True if directory
  uint64_t cacheHits = 0;        ///< Cache hits for file access
  uint64_t cacheMisses = 0;      ///< Cache misses for file access

  // PS4-specific fields
  PS4FileType fileType = PS4FileType::Regular; ///< PS4 file type
  uint32_t ps4Permissions = 0;                 ///< PS4-specific permissions
  std::string mountPoint;             ///< Mount point (e.g., /app0, /dev)
  bool isEncrypted = false;           ///< Encrypted file flag
  std::vector<uint8_t> encryptionKey; ///< Encryption key for file
  uint64_t blockSize = 4096;          ///< Block size for PFS
  std::vector<uint8_t> checksum;      ///< File integrity checksum
  bool present = true;                ///< File is present flag
};

/**
 * @brief Structure representing an open file handle.
 */
struct FileHandle {
  std::string path;         ///< Virtual file path
  uint32_t flags = 0;       ///< Open flags (e.g., O_RDONLY)
  uint64_t offset = 0;      ///< Current file offset
  int hostFd = -1;          ///< Host OS file descriptor
  int fd = -1;              ///< Emulator file descriptor
  uint64_t cacheHits = 0;   ///< Cache hits for read/write
  uint64_t cacheMisses = 0; ///< Cache misses for read/write
};

/**
 * @brief Filesystem statistics.
 */
struct FilesystemStats {
  uint64_t operationCount = 0;  ///< Total filesystem operations
  uint64_t totalLatencyUs = 0;  ///< Total latency (microseconds)
  uint64_t cacheHits = 0;       ///< Cache hits for file operations
  uint64_t cacheMisses = 0;     ///< Cache misses for file operations
  uint64_t fileAccessCount = 0; ///< Total file accesses
};

/**
 * @brief Configuration settings for the PS4 filesystem.
 */
struct Settings {
  std::string defaultMountPoint;          ///< Default mount point path
  std::string saveDataPath = "/savedata"; ///< Save data mount point
  std::string trophyPath = "/trophy";     ///< Trophy data mount point
  std::string systemPath = "/system";     ///< System mount point
  bool enableDeviceFiles = true;          ///< Enable device file emulation
  bool enablePFS = true;                  ///< Enable PlayStation File System
};

/**
 * @brief Manages the PS4 virtual filesystem.
 * @details Handles file operations, directory mounting, and game loading, with
 * thread-safe access and metrics.
 */
class PS4Filesystem {
public:
  /**
   * @brief Constructs the filesystem with an emulator reference.
   * @param emu Reference to the PS4 emulator.
   */
  explicit PS4Filesystem(PS4Emulator &emu);

  /**
   * @brief Default constructor (for testing purposes).
   */
  PS4Filesystem();

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~PS4Filesystem();

  /**
   * @brief Initializes the filesystem.
   * @return True on success, false otherwise.
   */
  bool Initialize();

  /**
   * @brief Shuts down the filesystem, closing handles.
   */
  void Shutdown();

  /**
   * @brief Opens a file with specified flags and mode.
   * @param path Virtual file path.
   * @param flags Open flags (e.g., O_RDONLY).
   * @param mode File permissions.
   * @return File descriptor, or -1 on failure.
   */
  int OpenFile(const std::string &path, int flags, mode_t mode);

  /**
   * @brief Closes a file descriptor.
   * @param fd File descriptor.
   * @return 0 on success, -1 on failure.
   */
  int CloseFile(int fd);

  /**
   * @brief Reads data from a file.
   * @param fd File descriptor.
   * @param buf Output buffer.
   * @param count Bytes to read.
   * @return Bytes read, or -1 on failure.
   */
  ssize_t ReadFile(int fd, void *buf, size_t count);

  /**
   * @brief Reads entire file contents into a vector.
   * @param path File path.
   * @param data Output vector to store file data.
   * @return True on success, false on failure.
   */
  bool ReadFile(const std::string &path, std::vector<uint8_t> &data);

  /**
   * @brief Lists all files in a directory.
   * @param path Directory path.
   * @param recursive Whether to list files recursively.
   * @return Vector of file paths.
   */
  std::vector<std::string> ListFiles(const std::string &path,
                                     bool recursive = false);

  /**
   * @brief Writes data to a file.
   * @param fd File descriptor.
   * @param buf Input buffer.
   * @param count Bytes to write.
   * @return Bytes written, or -1 on failure.
   */
  ssize_t WriteFile(int fd, const void *buf, size_t count);

  /**
   * @brief Seeks to a position in a file.
   * @param fd File descriptor.
   * @param offset Offset to seek to.
   * @param whence Seek origin (SEEK_SET, SEEK_CUR, SEEK_END).
   * @return New offset, or -1 on failure.
   */
  off_t SeekFile(int fd, off_t offset, int whence);

  /**
   * @brief Retrieves file statistics.
   * @param path File path.
   * @param buf Output stat buffer.
   * @return 0 on success, -1 on failure.
   */
  int StatFile(const std::string &path, struct stat *buf);

  /**
   * @brief Creates a directory with specified mode.
   * @param path Directory path.
   * @param mode Directory permissions.
   * @return True on success, false otherwise.
   */
  bool CreateDirectory(const std::string &path, mode_t mode);
  /**
   * @brief Creates a directory (wide string).
   * @param path Directory path.
   * @return True on success, false otherwise.
   */
  bool CreateDirectoryW(const std::wstring &path);

  /**
   * @brief Mounts a directory.
   * @param path Directory path.
   * @return True on success, false otherwise.
   */
  bool MountDirectory(const std::wstring &path);

  /**
   * @brief Allocates virtual memory (delegates to OrbisOS).
   * @param size Size to allocate.
   * @param alignment Alignment requirement.
   * @param shared True if shared memory.
   * @return Virtual address, or 0 on failure.
   */
  uint64_t AllocateVirtualMemory(uint64_t size, uint64_t alignment,
                                 bool shared);

  /**
   * @brief Frees virtual memory (delegates to OrbisOS).
   * @param address Virtual address.
   * @return True on success, false otherwise.
   */
  bool FreeVirtualMemory(uint64_t address);

  /**
   * @brief Sets memory protection (delegates to OrbisOS).
   * @param address Virtual address.
   * @param size Size of region.
   * @param protection Protection flags.
   * @return True on success, false otherwise.
   */
  bool ProtectMemory(uint64_t address, uint64_t size, int protection);

  /**
   * @brief Gets the current process ID (delegates to OrbisOS).
   * @return Process ID.
   */
  uint64_t SceKernelGetProcessId();

  /**
   * @brief Dumps the filesystem state as a string.
   * @return String representation of the state.
   */
  std::string DumpState() const;

  /**
   * @brief Saves the filesystem state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the filesystem state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Retrieves filesystem statistics.
   * @return Current statistics.
   */
  FilesystemStats GetStats() const;

  /**
   * @brief Enhanced filesystem settings for PS4 emulation.
   */
  struct EnhancedSettings {
    std::string defaultMountPoint = "/app0"; ///< Default mount point
    int defaultFileMode = 0644;              ///< Default file permissions
    int defaultDirMode = 0755;               ///< Default directory permissions
    bool enableCaseSensitivity = false;      ///< Case sensitivity flag
    bool enableEncryption = false;           ///< Encryption enable flag
    uint64_t pfsBlockSize = 4096;            ///< Block size for PFS
    std::vector<std::string> additionalMounts; ///< Additional mount points
    std::string saveDataPath = "/savedata";    ///< Save data mount point
    std::string trophyPath = "/trophy";        ///< Trophy data mount point
    std::string systemPath = "/system";        ///< System mount point
    bool enableDeviceFiles = true;             ///< Enable device file emulation
    bool enablePFS = true;               ///< Enable PlayStation File System
    bool enablePermissionChecks = false; ///< Enable permission checks flag
  };

  /**
   * @brief Sets filesystem settings.
   * @param settings New settings.
   */
  void SetSettings(const EnhancedSettings &settings);

  /**
   * @brief Gets current filesystem settings.
   * @return Current settings.
   */
  const EnhancedSettings &GetSettings() const;

  /**
   * @brief Saves settings to a file.
   * @param filename File path.
   * @return True on success, false otherwise.
   */
  bool SaveSettings(const std::string &filename) const;

  /**
   * @brief Loads settings from a file.
   * @param filename File path.
   * @return True on success, false otherwise.
   */
  bool LoadSettings(const std::string &filename);

  /**
   * @brief Loads a game executable.
   * @param gamePath Path to the game executable.
   * @return True on success, false otherwise.
   */
  bool LoadGame(const std::string &gamePath);

  /**
   * @brief Starts the loaded game.
   * @return True on success, false otherwise.
   */
  bool StartGame();

  /**
   * @brief Gets the path of the loaded game.
   * @return Game path, or empty string if none loaded.
   */
  std::string GetLoadedGamePath() const;
  /**
   * @brief Checks if a game is loaded.
   * @return True if a game is loaded, false otherwise.
   */
  bool IsGameLoaded() const;

  /**
   * @brief Gets the game directory path.
   * @return Game directory path, or empty string if none set.
   */
  std::string GetGameDirectory() const;

  /**
   * @brief Sets the game directory path.
   * @param path Game directory path.
   */
  void SetGameDirectory(const std::string &path);

  /**
   * @brief PS4-specific filesystem methods.
   */
  void InitializeDeviceFiles();
  bool CreateDeviceFile(const std::string &path, PS4FileType deviceType);
  bool HandleDeviceAccess(const std::string &path, void *buffer, size_t size,
                          bool isWrite);
  PS4FileType DetermineFileType(const std::string &path) const;
  bool ValidatePS4Permissions(const std::string &path, int mode);
  std::string MapToHostPath(const std::string &virtualPath);
  bool InitializePFS();
  bool EncryptFile(const std::string &path, const std::vector<uint8_t> &key);
  bool DecryptFile(const std::string &path);

  /**
   * @brief Writes data to a file in the virtual filesystem.
   * @param path Virtual file path.
   * @param data Data to write.
   * @param size Size of data.
   * @return True on success, false otherwise.
   */
  bool WriteFile(const std::string &path, const void *data, size_t size);
  /**
   * @brief Creates a directory in the virtual filesystem.
   * @param path Directory path.
   * @return True on success, false otherwise.
   */
  bool CreateVirtualDirectory(const std::string &path);
  bool RemoveDirectory(const std::string &path);

private:
  PS4Emulator &m_emulator; ///< Reference to the PS4 emulator
  mutable std::shared_mutex
      m_mutex;            ///< Mutex for thread-safe filesystem access
  std::string m_rootPath; ///< Root path for virtual filesystem

  /**
   * @brief Resolves a virtual path to an actual filesystem path.
   * @param virtualPath Virtual path to resolve.
   * @return Resolved filesystem path.
   */
  std::string ResolvePath(const std::string &virtualPath) const;
  std::unordered_map<std::string, FileEntry> m_files; ///< Virtual file entries
  std::unordered_map<int, FileHandle> m_fileHandles;  ///< Open file handles
  std::vector<std::string> m_directories;             ///< Virtual directories
  std::unordered_map<std::string, PS4FileType> m_deviceFiles; ///< Device files
  std::unordered_map<std::string, std::string>
      m_mountPoints;                    ///< Mount points mapping
  int m_nextFd;                         ///< Next available file descriptor
  mutable FilesystemStats m_stats;      ///< Filesystem statistics
  EnhancedSettings m_settings;          ///< Filesystem configuration settings
  Settings m_configSettings;            ///< Filesystem configuration settings
  std::string m_loadedGamePath;         ///< Loaded game path
  std::string m_gameDirectory;          ///< Game directory path
  bool m_gameLoaded = false;            ///< Game loaded flag
  std::vector<uint8_t> m_encryptionKey; ///< Encryption key for file encryption

  /**
   * @brief Calculates checksum for file data.
   * @param data File data to calculate checksum for.
   * @return Checksum bytes.
   */
  std::vector<uint8_t>
  CalculateChecksum(const std::vector<uint8_t> &data) const;
};

} // namespace ps4